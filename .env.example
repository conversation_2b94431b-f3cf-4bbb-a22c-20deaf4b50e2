# Smart QR ID Scanner Configuration
# Copy this file to .env and modify the values as needed

# Flask Server Configuration
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
FLASK_DEBUG=False
FLASK_SECRET_KEY=your-secret-key-change-in-production

# Application Mode
# Set to 'false' to run as web server only (no desktop window)
# Set to 'true' to open desktop application window
DESKTOP_MODE=true

# Google OAuth2 Configuration (Recommended)
GOOGLE_CLIENT_ID=your-client-id-here.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-client-secret-here

# Traditional Email Configuration (Fallback)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USE_TLS=True
SENDER_EMAIL=<EMAIL>
SENDER_PASSWORD=your-app-password
SENDER_NAME=ID QR System

# Mobile and Cross-Device Settings
# These are automatically handled but can be customized
MOBILE_OPTIMIZED=true
TOUCH_FRIENDLY=true
RESPONSIVE_DESIGN=true

# Network Configuration
# For advanced users - allows custom network binding
# Leave empty for automatic detection
CUSTOM_HOST_IP=
ALLOW_EXTERNAL_CONNECTIONS=true

# Performance Settings
MAX_UPLOAD_SIZE_MB=10
QR_CODE_QUALITY=high
IMAGE_COMPRESSION=medium

# Security Settings
SECURE_COOKIES=false
SESSION_TIMEOUT_MINUTES=60
CSRF_PROTECTION=true
