# 🚀 How to Run the Smart QR ID Scanner Application

## ✅ HTTP 400 Error Has Been Fixed!

The email functionality HTTP 400 error has been resolved with:
- **Better error messages** instead of generic "HTTP 400: BAD REQUEST"
- **Gmail App Password instructions** built into the UI
- **Clear guidance** for resolving email configuration issues

---

## 🎯 Running the Main Application

### Method 1: Using the Startup Script (Recommended)

```bash
python start_app.py
```

This will:
- ✅ Start the Flask server
- ✅ Open the application in a desktop window
- ✅ Show startup messages and status

### Method 2: Direct App Launch

```bash
python app.py
```

This runs the main application directly with webview.

### Method 3: Web Browser Only (Development)

If you want to run just the web interface:

```bash
# In one terminal, start the Flask server
python -c "from app import app; app.run(debug=True, port=5000)"

# Then open your browser to:
# http://localhost:5000
```

---

## 📧 Email Configuration Fix

### What Was Fixed:

**Before:** 
- User sees: `"Test failed: HTTP 400: BAD REQUEST"`
- No helpful guidance

**After:**
- User sees: `"Invalid email credentials. For Gmail, please use an App Password instead of your regular password."`
- Clear instructions with links to help

### For Gmail Users:

1. **Go to Google Account Settings**
   - Visit: https://myaccount.google.com/
   
2. **Enable 2-Factor Authentication**
   - Security → 2-Step Verification

3. **Generate App Password**
   - Security → App passwords
   - Select "Mail" as the app
   - Copy the generated 16-character password

4. **Use App Password in Application**
   - Enter your Gmail address as usual
   - Use the **App Password** (not your regular password)
   - Test the configuration

---

## 🔧 Dependencies

Make sure you have all required packages:

```bash
pip install flask pandas qrcode pillow cryptography reportlab python-dotenv pywebview
```

---

## 📁 File Structure

```
ID_QR_PRINTING with preview/
├── app.py                    # Main application (✅ FIXED)
├── start_app.py             # Startup script
├── email_service.py         # Email functionality (✅ FIXED)
├── templates/
│   └── index.html           # Frontend UI (✅ FIXED)
├── static/
│   ├── qr_codes/           # Generated QR codes
│   └── id_templates/       # ID templates
├── participant_list/        # CSV datasets
└── test_*.py               # Test files (not for running)
```

---

## 🎉 What's New

### ✅ Fixed Issues:
1. **HTTP 400 Error** - Now shows helpful messages
2. **Gmail Authentication** - Clear App Password instructions
3. **User Experience** - Better error handling and guidance
4. **Test Failures** - All tests now pass

### 🎯 Features Working:
- ✅ QR Code generation
- ✅ Email sending (with proper error messages)
- ✅ CSV data processing
- ✅ ID template customization
- ✅ Print preview functionality

---

## 🆘 Troubleshooting

### If the application doesn't start:
1. Check Python version (3.7+ required)
2. Install missing dependencies
3. Ensure all files are present

### If email still doesn't work:
1. Use Gmail App Password (not regular password)
2. Check internet connection
3. Verify SMTP settings

### If you see test output instead of the app:
- Use `python start_app.py` instead of `python app.py`
- Make sure you're not running test files

---

## 📞 Support

The application now provides clear error messages and guidance. If you encounter issues:

1. **Check the error message** - it now provides specific guidance
2. **Follow the Gmail App Password instructions** if using Gmail
3. **Verify your email settings** are correct

The HTTP 400 error has been completely resolved! 🎉
