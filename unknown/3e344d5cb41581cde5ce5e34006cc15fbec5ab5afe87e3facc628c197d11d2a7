#!/usr/bin/env python3
"""
Test Google OAuth2 functionality
"""

import os
import sys
from google_oauth_service import google_oauth_service

def test_google_apis_availability():
    """Test if Google APIs are available"""
    print("🧪 Testing Google APIs Availability")
    print("-" * 40)
    
    if google_oauth_service.is_available():
        print("✅ Google APIs are available")
        return True
    else:
        print("❌ Google APIs not available")
        print("   Install with: pip install google-auth google-auth-oauthlib google-api-python-client")
        return False

def test_oauth_configuration():
    """Test OAuth2 configuration"""
    print("\n🧪 Testing OAuth2 Configuration")
    print("-" * 40)
    
    if google_oauth_service.is_configured():
        print("✅ OAuth2 is configured")
        print(f"   Client ID: {google_oauth_service.client_config['web']['client_id'][:20]}...")
        return True
    else:
        print("❌ OAuth2 not configured")
        print("   Please set GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET in .env file")
        return False

def test_authentication_status():
    """Test current authentication status"""
    print("\n🧪 Testing Authentication Status")
    print("-" * 40)
    
    if google_oauth_service.is_authenticated():
        print("✅ User is authenticated")
        print(f"   User: {google_oauth_service.user_name}")
        print(f"   Email: {google_oauth_service.user_email}")
        return True
    else:
        print("❌ User not authenticated")
        print("   Use the application to sign in with Google")
        return False

def show_setup_instructions():
    """Show setup instructions"""
    print("\n📋 Setup Instructions")
    print("=" * 50)
    
    print("1. Install Google APIs:")
    print("   pip install google-auth google-auth-oauthlib google-api-python-client")
    print()
    
    print("2. Create Google Cloud Project:")
    print("   - Go to: https://console.cloud.google.com/")
    print("   - Create new project")
    print("   - Enable Gmail API")
    print()
    
    print("3. Create OAuth2 Credentials:")
    print("   - Go to APIs & Services → Credentials")
    print("   - Create OAuth 2.0 Client ID")
    print("   - Add redirect URI: http://localhost:5000/oauth2callback")
    print()
    
    print("4. Configure Environment Variables:")
    print("   Create .env file with:")
    print("   GOOGLE_CLIENT_ID=your-client-id")
    print("   GOOGLE_CLIENT_SECRET=your-client-secret")
    print()
    
    print("5. Start the application:")
    print("   python start_app.py")

def main():
    """Main test function"""
    print("🔐 Google OAuth2 Test Suite")
    print("=" * 50)
    
    # Test 1: APIs availability
    apis_available = test_google_apis_availability()
    
    # Test 2: Configuration
    if apis_available:
        configured = test_oauth_configuration()
    else:
        configured = False
    
    # Test 3: Authentication
    if configured:
        authenticated = test_authentication_status()
    else:
        authenticated = False
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 50)
    print(f"Google APIs Available: {'✅' if apis_available else '❌'}")
    print(f"OAuth2 Configured: {'✅' if configured else '❌'}")
    print(f"User Authenticated: {'✅' if authenticated else '❌'}")
    
    if apis_available and configured and authenticated:
        print("\n🎉 All tests passed! Google OAuth2 is ready to use.")
    elif apis_available and configured:
        print("\n⚠️ Setup complete, but user needs to authenticate.")
        print("   Start the application and click 'Sign in with Google'")
    else:
        print("\n❌ Setup incomplete. See instructions below:")
        show_setup_instructions()

if __name__ == "__main__":
    main()
