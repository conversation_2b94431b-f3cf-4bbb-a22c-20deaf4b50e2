# 🔐 Google OAuth2 Setup Guide

## 🎯 Overview

This guide shows you how to set up "Sign in with Google" functionality for the Smart QR ID Scanner application. This is **much easier** than using App Passwords!

## ✅ Benefits of Google OAuth2

- ✅ **No App Passwords needed** - Just click "Sign in with Google"
- ✅ **More secure** - Uses Google's OAuth2 authentication
- ✅ **Easier for users** - One-click authentication
- ✅ **Better user experience** - No manual password entry
- ✅ **Automatic token refresh** - Stays authenticated longer

## 🚀 Setup Instructions

### Step 1: Install Required Dependencies

```bash
pip install google-auth google-auth-oauthlib google-api-python-client
```

### Step 2: Create Google Cloud Project

1. **Go to Google Cloud Console**
   - Visit: https://console.cloud.google.com/

2. **Create a New Project**
   - Click "New Project"
   - Name: "Smart QR ID Scanner"
   - Click "Create"

3. **Enable Gmail API**
   - Go to "APIs & Services" → "Library"
   - Search for "Gmail API"
   - Click "Enable"

### Step 3: Create OAuth2 Credentials

1. **Go to Credentials**
   - Navigate to "APIs & Services" → "Credentials"

2. **Create OAuth2 Client ID**
   - Click "Create Credentials" → "OAuth 2.0 Client IDs"
   - Application type: "Web application"
   - Name: "Smart QR ID Scanner"

3. **Configure Redirect URIs**
   - Authorized redirect URIs: `http://localhost:5000/oauth2callback`
   - Click "Create"

4. **Download Credentials**
   - Copy the **Client ID** and **Client Secret**

### Step 4: Configure Environment Variables

Create a `.env` file in your project directory:

```env
# Google OAuth2 Configuration
GOOGLE_CLIENT_ID=your-client-id-here.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-client-secret-here

# Traditional SMTP (fallback)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USE_TLS=True
SENDER_EMAIL=<EMAIL>
SENDER_PASSWORD=your-app-password
SENDER_NAME=ID QR System

# Application Settings
FLASK_SECRET_KEY=your-secret-key-change-in-production
```

### Step 5: Test the Setup

1. **Start the Application**
   ```bash
   python start_app.py
   ```

2. **Check OAuth2 Status**
   - Open the application
   - Look for the "Sign in with Google" section
   - If configured correctly, you'll see the blue Google sign-in button

3. **Test Authentication**
   - Click "Sign in with Google"
   - Complete the Google authentication flow
   - You should see "Signed in as: Your Name (<EMAIL>)"

## 🎨 User Interface

### When OAuth2 is Available:
- ✅ Blue "Sign in with Google" button
- ✅ User info display when authenticated
- ✅ Traditional email config is hidden

### When OAuth2 is Not Available:
- ✅ Falls back to traditional SMTP configuration
- ✅ Shows App Password instructions
- ✅ Works exactly as before

## 🔧 How It Works

### Authentication Flow:
1. User clicks "Sign in with Google"
2. Opens Google OAuth2 popup
3. User grants permissions
4. Application receives authentication tokens
5. Tokens are saved for future use

### Email Sending:
1. Application checks if user is authenticated with Google
2. If yes: Uses Gmail API (no SMTP needed)
3. If no: Falls back to traditional SMTP

### Automatic Fallback:
- If Google APIs not installed → Traditional SMTP
- If OAuth2 not configured → Traditional SMTP
- If user not authenticated → Traditional SMTP

## 🛠️ Troubleshooting

### "Google APIs not available"
```bash
pip install google-auth google-auth-oauthlib google-api-python-client
```

### "OAuth2 not configured"
- Check your `.env` file has `GOOGLE_CLIENT_ID` and `GOOGLE_CLIENT_SECRET`
- Verify the credentials are correct

### "Authentication failed"
- Check redirect URI is exactly: `http://localhost:5000/oauth2callback`
- Ensure Gmail API is enabled in Google Cloud Console

### "Permission denied"
- Make sure you granted all requested permissions during OAuth flow
- Try signing out and signing in again

## 📋 Required Google Permissions

The application requests these permissions:
- **Gmail Send** - To send QR code emails
- **User Email** - To display user's email address
- **User Profile** - To display user's name

## 🔒 Security Notes

- ✅ Tokens are stored locally and encrypted
- ✅ Automatic token refresh prevents expiration
- ✅ No passwords stored in the application
- ✅ Uses Google's secure OAuth2 flow
- ✅ Permissions can be revoked anytime in Google Account settings

## 🎉 Benefits for Users

### Before (App Passwords):
1. Enable 2FA on Google Account
2. Generate App Password
3. Copy 16-character password
4. Enter in application
5. Test configuration

### After (OAuth2):
1. Click "Sign in with Google"
2. Done! ✅

Much simpler and more secure!
