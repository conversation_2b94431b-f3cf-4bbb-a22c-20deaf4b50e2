:root {
    --primary-color: #2563eb;
    --success-color: #16a34a;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --bg-light: #f9fafb;
    --border-color: #e5e7eb;
    --neutral-dark: #1f2937;
    --neutral-light: #f3f4f6;
}

/* Responsive Design Variables */
:root {
    --primary-purple: #8B5CF6;
    --secondary-purple: #A855F7;
    --accent-purple: #C084FC;
    --light-purple: #DDD6FE;
    --dark-purple: #6B21A8;
    --gradient-primary: linear-gradient(135deg, #8B5CF6 0%, #A855F7 25%, #C084FC 50%, #DDD6FE 75%, #8B5CF6 100%);
    --gradient-secondary: linear-gradient(45deg, #6B21A8 0%, #8B5CF6 50%, #A855F7 100%);
    --shadow-light: 0 4px 20px rgba(139, 92, 246, 0.15);
    --shadow-medium: 0 8px 30px rgba(139, 92, 246, 0.25);
    --shadow-heavy: 0 15px 40px rgba(139, 92, 246, 0.35);
    --border-radius-sm: 8px;
    --border-radius-md: 16px;
    --border-radius-lg: 24px;
    --border-radius-xl: 32px;
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Enhanced Responsive Typography */
html {
    font-size: 16px;
}

/* Mobile First Approach */
@media (max-width: 320px) {
    html { font-size: 11px; }
}

@media (max-width: 480px) {
    html { font-size: 12px; }
}

@media (max-width: 768px) {
    html { font-size: 14px; }
}

/* Tablet */
@media (min-width: 769px) and (max-width: 1024px) {
    html { font-size: 15px; }
}

/* Desktop */
@media (min-width: 1025px) and (max-width: 1440px) {
    html { font-size: 16px; }
}

/* Large Desktop */
@media (min-width: 1441px) {
    html { font-size: 18px; }
}

/* Ultra-wide and 4K Desktop */
@media (min-width: 1920px) {
    html { font-size: 20px; }
}

@media (min-width: 2560px) {
    html { font-size: 22px; }
}

/* Orientation-specific optimizations */
@media (orientation: landscape) and (max-height: 600px) {
    /* Landscape mobile/tablet optimization */
    .container-fluid {
        padding: 0.5rem 1rem;
    }

    .modal-overlay > div {
        max-height: calc(100vh - 1rem);
        width: 90%;
    }

    .card {
        padding: 1rem;
    }

    .preview-section,
    .scanner-section {
        padding: 1rem;
    }
}

@media (orientation: portrait) and (max-width: 768px) {
    /* Portrait mobile optimization */
    .d-flex.flex-column.flex-lg-row {
        flex-direction: column !important;
    }

    .preview-section,
    .scanner-section {
        max-width: 100% !important;
        flex: none !important;
    }
}

/* Device-specific optimizations */
/* iPhone and small Android phones */
@media only screen and (max-width: 414px) {
    .settingbtn {
        position: fixed;
        top: 1rem;
        right: 1rem;
        z-index: 1000;
    }

    .container-fluid {
        padding-top: 5rem; /* Account for fixed settings button */
    }
}

/* iPad specific */
@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) {
    /* Optimize for iPad touch interactions */
    .btn, button, input, select {
        -webkit-appearance: none;
        appearance: none;
        border-radius: 8px;
    }

    /* Better hover states for iPad */
    .btn:hover, button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
}

/* Device-specific CSS classes (added by JavaScript) */
.device-mobile {
    --touch-target-size: 48px;
}

.device-tablet {
    --touch-target-size: 44px;
}

.device-desktop {
    --touch-target-size: 40px;
}

.touch-device .btn,
.touch-device button,
.touch-device input,
.touch-device select {
    min-height: var(--touch-target-size, 44px);
}

/* Orientation-specific adjustments */
.orientation-landscape.device-mobile .modal-overlay {
    padding: 0.5rem;
}

.orientation-landscape.device-mobile .modal-overlay > div {
    max-height: calc(100vh - 1rem);
    overflow-y: auto;
}

.orientation-portrait.device-mobile .d-flex.flex-column.flex-lg-row {
    flex-direction: column !important;
}

/* Screen size specific optimizations */
.screen-xs .container-fluid {
    padding: 0.5rem;
}

.screen-sm .container-fluid {
    padding: 0.75rem;
}

.screen-md .container-fluid {
    padding: 1rem;
}

.screen-lg .container-fluid {
    padding: 1.5rem;
}

.screen-xl .container-fluid {
    padding: 2rem;
}

.screen-xxl .container-fluid {
    padding: 3rem;
}

/* Enhanced Camera Scanner Styling */
.camera-container {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: 2px dashed #dee2e6;
    border-radius: 16px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    /* Flexible sizing that adapts to container */
    width: 100%;
    max-width: min(350px, 85vw);
    height: auto;
    aspect-ratio: 1;
    min-height: 200px;
    max-height: min(350px, 70vh);
}

.camera-container.scanning {
    border-color: var(--primary-purple);
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.08) 0%, rgba(139, 92, 246, 0.03) 100%);
    box-shadow: 0 8px 32px rgba(139, 92, 246, 0.2);
}

/* Enhanced video element control */
#reader video {
    max-width: 100% !important;
    max-height: 100% !important;
    width: 100% !important;
    height: 100% !important;
    object-fit: cover;
    border-radius: 12px;
    background: #000;
}

/* Enhanced QR Code scanner canvas styling */
#reader canvas {
    max-width: 100% !important;
    max-height: 100% !important;
    width: 100% !important;
    height: 100% !important;
    border-radius: 12px;
    object-fit: contain;
}

/* Enhanced scanner overlay styling */
#reader > div {
    width: 100% !important;
    height: 100% !important;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

/* Camera placeholder styling */
#camera-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    transition: all 0.3s ease;
}

#camera-placeholder:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    color: var(--primary-purple);
}

/* Responsive camera container - Mobile First */
/* Extra Small Devices (phones, 320px and up) */
@media (max-width: 320px) {
    .camera-container {
        max-width: 95vw;
        min-height: 180px;
        max-height: min(280px, 60vh);
        border-radius: 12px;
    }
}

/* Small Devices (phones, 480px and up) */
@media (max-width: 480px) {
    .camera-container {
        max-width: min(280px, 90vw);
        min-height: 200px;
        max-height: min(320px, 65vh);
        border-radius: 14px;
    }
}

/* Medium Devices (tablets, 768px and up) */
@media (max-width: 768px) {
    .camera-container {
        max-width: min(320px, 85vw);
        min-height: 220px;
        max-height: min(350px, 70vh);
        border-radius: 16px;
    }
}

/* Large Devices (desktops, 992px and up) */
@media (min-width: 992px) {
    .camera-container {
        max-width: min(380px, 80vw);
        min-height: 250px;
        max-height: min(380px, 75vh);
    }
}

/* Extra Large Devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
    .camera-container {
        max-width: min(400px, 75vw);
        min-height: 280px;
        max-height: min(400px, 80vh);
    }
}

body {
    background: var(--gradient-primary);
    background-size: 400% 400%;
    animation: gradientShift 20s ease infinite;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    font-size: 1rem;
    color: var(--neutral-dark);
    min-height: 100vh;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    line-height: 1.6;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    25% { background-position: 100% 50%; }
    50% { background-position: 100% 100%; }
    75% { background-position: 0% 100%; }
    100% { background-position: 0% 50%; }
}

.main-container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.05);
    padding: 2rem;
    margin: 40px auto;
    max-width: 1200px;
}

.header {
    background: linear-gradient(90deg, var(--primary-color), #3b82f6);
    color: white;
    padding: 2rem 1rem;
    text-align: center;
    border-radius: 12px;
    margin-bottom: 2rem;
}

body.bg-light {
    background: linear-gradient(to bottom right, #f9fafc, #e5e7eb);
}

/* Enhanced Responsive Container */
.container-fluid {
    padding: 0.5rem;
    max-width: 100%;
    overflow-x: hidden;
}

/* Mobile First Responsive Padding */
@media (min-width: 320px) {
    .container-fluid { padding: 0.75rem; }
}

@media (min-width: 480px) {
    .container-fluid { padding: 1rem; }
}

@media (min-width: 576px) {
    .container-fluid { padding: 1.25rem; }
}

@media (min-width: 768px) {
    .container-fluid { padding: 1.5rem; }
}

@media (min-width: 992px) {
    .container-fluid { padding: 2rem; }
}

@media (min-width: 1200px) {
    .container-fluid { padding: 2.5rem; }
}

@media (min-width: 1400px) {
    .container-fluid { padding: 3rem; }
}

/* Enhanced Desktop Experience */
@media (min-width: 1200px) {
    .container-fluid {
        max-width: 1400px;
        margin: 0 auto;
    }

    .card {
        padding: 3rem;
        border-radius: 25px;
    }

    .preview-section,
    .scanner-section {
        padding: 3rem;
        border-radius: 20px;
    }

    /* Better proportions for large screens */
    .d-flex.flex-column.flex-lg-row {
        gap: 3rem;
    }

    .preview-section {
        flex: 1 1 65%;
        max-width: 65%;
    }

    .scanner-section {
        flex: 1 1 35%;
        max-width: 35%;
        min-width: 400px;
    }
}

/* Ultra-wide Desktop (1920px+) */
@media (min-width: 1920px) {
    .container-fluid {
        max-width: 1600px;
        padding: 4rem;
    }

    .card {
        padding: 4rem;
        border-radius: 30px;
    }

    .preview-section,
    .scanner-section {
        padding: 4rem;
        border-radius: 25px;
    }

    .d-flex.flex-column.flex-lg-row {
        gap: 4rem;
    }

    /* Larger buttons for desktop */
    .btn {
        padding: 1rem 2rem;
        font-size: 1.1rem;
        border-radius: 12px;
    }

    .btn-sm {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
}

/* 4K and Ultra-wide (2560px+) */
@media (min-width: 2560px) {
    .container-fluid {
        max-width: 2000px;
        padding: 5rem;
    }

    .card {
        padding: 5rem;
        border-radius: 35px;
    }

    .preview-section,
    .scanner-section {
        padding: 5rem;
        border-radius: 30px;
    }
}

/* Enhanced Cards */
.card {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-medium);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(15px);
    background: rgba(255, 255, 255, 0.95);
    transition: all var(--transition-medium);
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-secondary);
    opacity: 0;
    transition: opacity var(--transition-medium);
}

.card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-heavy);
    border-color: rgba(139, 92, 246, 0.3);
}

.card:hover::before {
    opacity: 1;
}

/* Responsive Card Spacing */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
        border-radius: var(--border-radius-md);
    }

    .card:hover {
        transform: translateY(-4px) scale(1.01);
    }
}

/* Responsive Sections */
.preview-section,
.scanner-section {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-medium);
    border: 1px solid rgba(139, 92, 246, 0.2);
    backdrop-filter: blur(20px);
    transition: all var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.preview-section::before,
.scanner-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-secondary);
    transform: scaleX(0);
    transition: transform var(--transition-medium);
}

.preview-section:hover,
.scanner-section:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
    border-color: rgba(139, 92, 246, 0.4);
}

.preview-section:hover::before,
.scanner-section:hover::before {
    transform: scaleX(1);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .preview-section,
    .scanner-section {
        padding: 1.5rem;
        border-radius: var(--border-radius-md);
        margin-bottom: 1rem;
    }

    .preview-section:hover,
    .scanner-section:hover {
        transform: translateY(-3px);
    }
}

@media (max-width: 480px) {
    .preview-section,
    .scanner-section {
        padding: 1rem;
        border-radius: var(--border-radius-sm);
    }
}

/* Enhanced Section Titles */
.section-title {
    background: var(--gradient-secondary);
    background-size: 200% 200%;
    animation: gradientShift 10s ease infinite;
    color: white;
    padding: 1rem 2rem;
    font-size: 1.2rem;
    font-weight: 700;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-medium);
    margin-bottom: 2rem;
    display: inline-block;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    transition: all var(--transition-medium);
    position: relative;
    overflow: hidden;
    letter-spacing: 0.5px;
}

.section-title::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.8s ease;
}

.section-title:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-heavy);
}

.section-title:hover::before {
    left: 100%;
}

/* Responsive Section Titles */
@media (max-width: 768px) {
    .section-title {
        font-size: 1rem;
        padding: 0.8rem 1.5rem;
        border-radius: var(--border-radius-lg);
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 480px) {
    .section-title {
        font-size: 0.9rem;
        padding: 0.6rem 1rem;
        border-radius: var(--border-radius-md);
        margin-bottom: 1rem;
    }
}

#current-dataset,
#current-template {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Enhanced Modal overlay styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 1rem;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-overlay > div {
    width: 100%;
    max-width: 600px;
    max-height: calc(100vh - 2rem);
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    position: relative;
    display: flex;
    flex-direction: column;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.8);
    transform: scale(0.9) translateY(20px);
    transition: all 0.3s ease;
}

.modal-overlay.show > div {
    transform: scale(1) translateY(0);
}

.modal-content {
    overflow-y: auto;
    padding: 2rem;
    flex: 1;
    /* Custom scrollbar for better UX */
    scrollbar-width: thin;
    scrollbar-color: var(--primary-purple) transparent;
}

.modal-content::-webkit-scrollbar {
    width: 6px;
}

.modal-content::-webkit-scrollbar-track {
    background: transparent;
}

.modal-content::-webkit-scrollbar-thumb {
    background: var(--primary-purple);
    border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
    background: rgba(139, 92, 246, 0.8);
}

/* Improved modal content spacing */
.modal-content h3 {
    margin-bottom: 1rem;
    color: var(--neutral-dark);
    font-weight: 600;
}

.modal-content h4 {
    margin-bottom: 0.75rem;
    margin-top: 1.5rem;
    color: var(--primary-purple);
    font-weight: 500;
    font-size: 1.1rem;
}

.modal-content .form-group {
    margin-bottom: 1rem;
}

.modal-content .alert {
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

/* Enhanced Responsive Modal Adjustments */

/* Extra Small Devices (phones, 320px and up) */
@media (max-width: 320px) {
    .modal-overlay {
        padding: 0.5rem;
    }

    .modal-overlay > div {
        width: 100%;
        max-height: calc(100vh - 1rem);
        border-radius: 12px;
        margin: 0;
    }

    .modal-content {
        padding: 1rem;
        font-size: 0.9rem;
    }

    .modal-content h4 {
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }

    .modal-content .btn {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
        min-height: 44px;
    }
}

/* Small Devices (phones, 480px and up) */
@media (max-width: 480px) {
    .modal-overlay {
        padding: 0.75rem;
    }

    .modal-overlay > div {
        width: 95%;
        max-height: calc(100vh - 1.5rem);
        border-radius: 16px;
    }

    .modal-content {
        padding: 1.25rem;
        font-size: 0.95rem;
    }

    .modal-content .btn {
        min-height: 48px;
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* Medium Devices (tablets, 768px and up) */
@media (max-width: 768px) {
    .modal-overlay {
        padding: 1rem;
    }

    .modal-overlay > div {
        width: 90%;
        max-height: calc(100vh - 2rem);
        border-radius: 18px;
    }

    .modal-content {
        padding: 1.5rem;
    }
}

/* Large Devices (tablets, 1024px and up) */
@media (min-width: 768px) and (max-width: 1024px) {
    .modal-overlay {
        padding: 1.5rem;
    }

    .modal-overlay > div {
        width: 80%;
        max-width: 650px;
        max-height: calc(100vh - 3rem);
        border-radius: 20px;
    }

    .modal-content {
        padding: 2rem;
        max-height: calc(100vh - 7rem);
    }

    #activatePanel {
        padding: 2.5rem;
    }
}

/* Extra Large Devices (desktops, 1200px and up) */
@media (min-width: 1200px) {
    .modal-overlay {
        padding: 2rem;
    }

    .modal-overlay > div {
        width: 70%;
        max-width: 700px;
        max-height: calc(100vh - 4rem);
        border-radius: 24px;
    }

    .modal-content {
        padding: 2.5rem;
        max-height: calc(100vh - 8rem);
    }
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 600px) {
    .modal-overlay {
        padding: 0.5rem;
        align-items: flex-start;
        padding-top: 1rem;
    }

    .modal-overlay > div {
        max-height: calc(100vh - 1rem);
        width: 85%;
        max-width: 800px;
    }

    .modal-content {
        padding: 1rem 1.5rem;
        max-height: calc(100vh - 3rem);
    }
}

/* Email configuration controls */
.email-config-control .row {
    margin: 0;
}

.email-config-control .col-md-6,
.email-config-control .col-md-4 {
    padding: 0.25rem;
    margin-bottom: 0.5rem;
}

/* Enhanced Modal Close Button */
.close-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    cursor: pointer;
    color: #6b7280;
    transition: all 0.3s ease;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    /* Touch-friendly sizing */
    min-width: 44px;
    min-height: 44px;
}

.close-btn:hover {
    color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
    transform: scale(1.05);
}

.close-btn:active {
    transform: scale(0.95);
}

/* Responsive close button */
@media (max-width: 480px) {
    .close-btn {
        top: 0.75rem;
        right: 0.75rem;
        width: 36px;
        height: 36px;
        font-size: 1.1rem;
        min-width: 44px;
        min-height: 44px;
    }
}

@media (max-width: 320px) {
    .close-btn {
        top: 0.5rem;
        right: 0.5rem;
        width: 32px;
        height: 32px;
        font-size: 1rem;
        min-width: 44px;
        min-height: 44px;
    }
}

.paper-size-note {
    font-size: 0.85rem;
    color: #6b7280;
    margin-top: 0.5rem;
}

.email-config-note {
    font-size: 0.85rem;
    color: #6b7280;
    margin-top: 0.5rem;
}

.email-config-control {
    background: linear-gradient(135deg, rgba(248, 249, 250, 0.9) 0%, rgba(233, 236, 239, 0.8) 100%);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid rgba(233, 236, 239, 0.5);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.email-config-control:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.email-config-control input {
    border: 1px solid #ced4da;
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.email-config-control input:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: 0;
}

#emailSection {
    background: linear-gradient(135deg, rgba(227, 242, 253, 0.9) 0%, rgba(243, 229, 245, 0.9) 100%);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid rgba(225, 190, 231, 0.5);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    animation: emailSectionGlow 3s ease-in-out infinite alternate;
}

@keyframes emailSectionGlow {
    0% { box-shadow: 0 0 10px rgba(171, 71, 188, 0.2); }
    100% { box-shadow: 0 0 20px rgba(171, 71, 188, 0.4); }
}

#emailSection input {
    border: 1px solid #ce93d8;
    border-radius: 6px;
}

#emailSection input:focus {
    border-color: #ab47bc;
    box-shadow: 0 0 0 0.2rem rgba(171, 71, 188, 0.25);
}

#sendEmailBtn {
    background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
    border: none;
    border-radius: 6px;
    padding: 0.5rem 1rem;
    color: white;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

#sendEmailBtn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.4);
}

#sendEmailBtn:disabled {
    opacity: 0.6;
    transform: none;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

#noEmailSection {
    background: linear-gradient(135deg, rgba(255, 243, 205, 0.9) 0%, rgba(255, 235, 238, 0.9) 100%);
    border-radius: 15px;
    padding: 1rem;
    border: 1px solid rgba(255, 193, 7, 0.3);
    backdrop-filter: blur(10px);
}

#noEmailSection .alert {
    background: transparent;
    border: none;
    margin: 0;
    padding: 0;
    color: #856404;
}

#noEmailSection .alert i {
    color: #ffc107;
}

/* Email input enhancements */
#recipientEmail {
    transition: all 0.3s ease;
}

#recipientEmail:focus {
    transform: scale(1.02);
    box-shadow: 0 0 15px rgba(171, 71, 188, 0.4);
}

#recipientEmail.auto-populated {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(129, 199, 132, 0.1) 100%);
    border-color: #4caf50;
}

/* Enhanced alert styles */
.alert-info {
    background: linear-gradient(135deg, rgba(13, 202, 240, 0.1) 0%, rgba(25, 135, 84, 0.1) 100%);
    border: 1px solid rgba(13, 202, 240, 0.3);
    color: #055160;
}

.alert-success {
    background: linear-gradient(135deg, rgba(25, 135, 84, 0.1) 0%, rgba(32, 201, 151, 0.1) 100%);
    border: 1px solid rgba(25, 135, 84, 0.3);
    color: #0a3622;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 235, 59, 0.1) 100%);
    border: 1px solid rgba(255, 193, 7, 0.3);
    color: #664d03;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(244, 67, 54, 0.1) 100%);
    border: 1px solid rgba(220, 53, 69, 0.3);
    color: #58151c;
}

.alert-info {
    background: linear-gradient(135deg, rgba(13, 110, 253, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
    border: 1px solid rgba(139, 92, 246, 0.3);
    color: #0c4a6e;
}

/* Improved notification positioning and styling */
.position-fixed.alert {
    animation: slideInRight 0.3s ease-out;
    backdrop-filter: blur(10px);
    font-weight: 500;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive First Run Setup Styles */
.setup-container {
    max-width: 900px;
    margin: 1rem auto;
    padding: 2rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-heavy);
    backdrop-filter: blur(25px);
    border: 1px solid rgba(139, 92, 246, 0.2);
    position: relative;
    overflow: hidden;
    animation: slideInUp 0.8s ease-out;
}

.setup-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-secondary);
}

/* Responsive Setup Container */
@media (max-width: 768px) {
    .setup-container {
        margin: 0.5rem;
        padding: 1.5rem;
        border-radius: var(--border-radius-lg);
    }
}

@media (max-width: 480px) {
    .setup-container {
        margin: 0.25rem;
        padding: 1rem;
        border-radius: var(--border-radius-md);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Header Logo */
.header-logo {
    text-align: center;
    margin-bottom: 2rem;
    animation: fadeInDown 1s ease-out;
}

.header-logo img {
    width: 100px;
    height: 100px;
    margin-bottom: 1.5rem;
    border-radius: 50%;
    box-shadow: var(--shadow-medium);
    transition: all var(--transition-medium);
    border: 3px solid rgba(139, 92, 246, 0.3);
}

.header-logo img:hover {
    transform: scale(1.15) rotate(10deg);
    box-shadow: var(--shadow-heavy);
    border-color: rgba(139, 92, 246, 0.6);
}

.header-logo h1 {
    background: var(--gradient-secondary);
    background-size: 200% 200%;
    animation: gradientShift 12s ease infinite;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    font-size: 3rem;
    margin: 0;
    letter-spacing: 1px;
    text-shadow: 0 4px 8px rgba(139, 92, 246, 0.3);
}

/* Responsive Header */
@media (max-width: 768px) {
    .header-logo img {
        width: 80px;
        height: 80px;
        margin-bottom: 1rem;
    }

    .header-logo h1 {
        font-size: 2.5rem;
    }
}

@media (max-width: 480px) {
    .header-logo img {
        width: 60px;
        height: 60px;
        margin-bottom: 0.8rem;
    }

    .header-logo h1 {
        font-size: 2rem;
        letter-spacing: 0.5px;
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Setup Steps */
.step {
    margin-bottom: 2rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: var(--border-radius-lg);
    border: 1px solid rgba(139, 92, 246, 0.2);
    transition: all var(--transition-medium);
    position: relative;
    overflow: hidden;
    animation: fadeInLeft 0.6s ease-out;
}

.step:nth-child(even) {
    animation: fadeInRight 0.6s ease-out;
}

.step::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--gradient-secondary);
    transform: scaleY(0);
    transition: transform var(--transition-medium);
}

.step:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: var(--shadow-medium);
    border-color: rgba(139, 92, 246, 0.4);
}

.step:hover::before {
    transform: scaleY(1);
}

.step h3 {
    color: var(--dark-purple);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    font-weight: 700;
    font-size: 1.3rem;
}

.step-number {
    background: var(--gradient-secondary);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.4rem;
    box-shadow: var(--shadow-light);
    transition: all var(--transition-medium);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.step:hover .step-number {
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--shadow-medium);
}

/* Responsive Steps */
@media (max-width: 768px) {
    .step {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border-radius: var(--border-radius-md);
    }

    .step h3 {
        font-size: 1.1rem;
        gap: 1rem;
    }

    .step-number {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .step {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .step h3 {
        font-size: 1rem;
        gap: 0.8rem;
        flex-direction: column;
        text-align: center;
    }

    .step-number {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive Upload Area */
.upload-area {
    border: 3px dashed var(--primary-purple);
    border-radius: var(--border-radius-lg);
    padding: 3rem 2rem;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-medium);
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.05) 0%, rgba(168, 85, 247, 0.05) 100%);
    position: relative;
    overflow: hidden;
}

.upload-area::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(139, 92, 246, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all var(--transition-slow);
}

.upload-area:hover {
    border-color: var(--secondary-purple);
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(168, 85, 247, 0.1) 100%);
    transform: translateY(-5px) scale(1.02);
    box-shadow: var(--shadow-medium);
}

.upload-area:hover::before {
    width: 300px;
    height: 300px;
}

.upload-area i {
    transition: all var(--transition-medium);
    color: var(--primary-purple);
    font-size: 3rem;
    margin-bottom: 1rem;
}

.upload-area:hover i {
    transform: scale(1.2) rotate(10deg);
    color: var(--secondary-purple);
}

.upload-area p {
    color: var(--dark-purple);
    font-weight: 600;
    margin: 0;
    font-size: 1.1rem;
}

/* Responsive Upload Area */
@media (max-width: 768px) {
    .upload-area {
        padding: 2rem 1.5rem;
        border-radius: var(--border-radius-md);
    }

    .upload-area i {
        font-size: 2.5rem;
    }

    .upload-area p {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .upload-area {
        padding: 1.5rem 1rem;
        border-width: 2px;
    }

    .upload-area i {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .upload-area p {
        font-size: 0.9rem;
    }
}

.file-list {
    max-height: 200px;
    overflow-y: auto;
    border-radius: 10px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.list-group-item {
    border: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.list-group-item:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

.footer {
    text-align: center;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.footer img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
}

/* Enhanced Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(107, 33, 168, 0.95) 0%, rgba(139, 92, 246, 0.95) 100%);
    backdrop-filter: blur(20px);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    animation: fadeIn var(--transition-medium) ease;
}

.loading-overlay.show {
    display: flex;
}

/* Enhanced Spinner from Uiverse.io */
.spinner {
    --gap: 8px;
    --clr: var(--accent-purple);
    --height: 30px;
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--gap);
    padding: 2rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-heavy);
}

.spinner span {
    background: linear-gradient(135deg, var(--primary-purple), var(--secondary-purple));
    width: 8px;
    height: var(--height);
    border-radius: 4px;
    animation: grow 1.2s ease-in-out infinite;
    box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}

.spinner span:nth-child(1) {
    animation-delay: 0s;
}

.spinner span:nth-child(2) {
    animation-delay: 0.15s;
}

.spinner span:nth-child(3) {
    animation-delay: 0.3s;
}

.spinner span:nth-child(4) {
    animation-delay: 0.45s;
}

@keyframes grow {
    0%, 100% {
        transform: scaleY(0.3);
        opacity: 0.7;
    }
    50% {
        transform: scaleY(1.8);
        opacity: 1;
    }
}

/* Loading Text */
.loading-text {
    position: absolute;
    bottom: 30%;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
    text-align: center;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Page Transition Styles */
.page-transition {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    background-size: 400% 400%;
    animation: gradientShift 3s ease infinite;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-medium);
}

.page-transition.show {
    opacity: 1;
    visibility: visible;
}

.transition-content {
    text-align: center;
    color: white;
    animation: bounceIn 1s ease-out;
}

.transition-logo img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    box-shadow: var(--shadow-heavy);
    margin-bottom: 2rem;
    animation: rotateIn 1.5s ease-out;
    border: 4px solid rgba(255, 255, 255, 0.3);
}

.transition-text h2 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    animation: slideInUp 1s ease-out 0.3s both;
}

.transition-text p {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    animation: slideInUp 1s ease-out 0.6s both;
}

.transition-progress {
    width: 300px;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
    margin: 0 auto;
    animation: slideInUp 1s ease-out 0.9s both;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 1));
    border-radius: 3px;
    width: 0%;
    animation: progressFill 2s ease-out 1.2s forwards;
}

/* Responsive Transition */
@media (max-width: 768px) {
    .transition-logo img {
        width: 100px;
        height: 100px;
        margin-bottom: 1.5rem;
    }

    .transition-text h2 {
        font-size: 2rem;
    }

    .transition-text p {
        font-size: 1rem;
    }

    .transition-progress {
        width: 250px;
    }
}

@media (max-width: 480px) {
    .transition-logo img {
        width: 80px;
        height: 80px;
        margin-bottom: 1rem;
    }

    .transition-text h2 {
        font-size: 1.5rem;
    }

    .transition-text p {
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
    }

    .transition-progress {
        width: 200px;
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes rotateIn {
    0% {
        opacity: 0;
        transform: rotate(-200deg) scale(0);
    }
    100% {
        opacity: 1;
        transform: rotate(0deg) scale(1);
    }
}

@keyframes progressFill {
    0% { width: 0%; }
    100% { width: 100%; }
}

#activatePanel {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    padding: 2.5rem;
    border-radius: 25px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    transition: all 0.3s ease;
}

#activatePanel:hover {
    transform: translateY(-3px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

#activatePanel strong {
    font-size: 1rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(90deg, #782fff, #c084fc);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(120, 47, 255, 0.2);
    margin-bottom: 0.5rem;
}

#activatePanel span {
    font-family: monospace;
    font-size: 0.9rem;
    color: #1f2937;
    word-break: break-all;
}

/* Modal buttons - simplified and smaller */
#activatePanel .btn-outline-primary {
    padding: 0.4rem 1rem;
    font-size: 0.85rem;
    font-weight: 500;
    border-radius: 8px;
    border: 1px solid var(--primary-purple);
    background: white;
    color: var(--primary-purple);
    transition: all 0.2s ease;
    margin: 0.25rem;
}

#activatePanel .btn-outline-primary:hover {
    background: var(--primary-purple);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.2);
}

/* Consistent button spacing in modals */
#activatePanel .btn {
    margin: 0.25rem;
}

#activatePanel .d-flex.gap-2 {
    gap: 0.5rem !important;
}

#activatePanel .btn-success {
    background: var(--success-color);
    border: none;
    color: white;
    font-weight: 500;
    padding: 0.4rem 1rem;
    border-radius: 8px;
    font-size: 0.85rem;
    transition: all 0.2s ease;
    margin: 0.25rem;
}

#activatePanel .btn-success:hover {
    background: #047857;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(22, 163, 74, 0.2);
}

/* Consistent styling for all modal buttons */
#activatePanel .btn-sm {
    padding: 0.3rem 0.8rem;
    font-size: 0.8rem;
    border-radius: 6px;
    margin: 0.2rem;
}

#activatePanel .btn-warning {
    background: var(--warning-color);
    border: none;
    color: white;
    font-weight: 500;
    padding: 0.4rem 1rem;
    border-radius: 8px;
    font-size: 0.85rem;
    transition: all 0.2s ease;
    margin: 0.25rem;
}

#activatePanel .btn-warning:hover {
    background: #b45309;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(217, 119, 6, 0.2);
}

#activatePanel .paper-size-control {
    margin-top: 2rem;
}

#activatePanel label {
    font-weight: 600;
    font-size: 0.95rem;
    color: #374151;
    margin-bottom: 0.25rem;
}

#activatePanel select {
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    background: #f9fafb;
    font-size: 0.9rem;
    width: 100%;
    max-width: 300px;
}

#customSizeFields {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 8px;
    background: #f9f9fb;
    border: 1px solid #e5e7eb;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

#customSizeFields input {
    width: 140px;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    border: 1px solid #d1d5db;
    background: white;
    font-size: 0.9rem;
}

/* Enhanced Touch-Friendly Buttons and Inputs */
.btn, button, input[type="button"], input[type="submit"] {
    min-height: 44px; /* Apple's recommended touch target size */
    min-width: 44px;
    touch-action: manipulation; /* Prevents zoom on double-tap */
    -webkit-tap-highlight-color: rgba(139, 92, 246, 0.2);
    /* Enhanced touch feedback */
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

/* Touch ripple effect */
.btn::before, button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
    pointer-events: none;
}

.btn:active::before, button:active::before {
    width: 300px;
    height: 300px;
}

/* Enhanced focus states for accessibility */
.btn:focus, button:focus, input:focus, select:focus, textarea:focus {
    outline: 2px solid var(--primary-purple);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.2);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .btn, button {
        border: 2px solid currentColor;
    }

    .btn:focus, button:focus, input:focus, select:focus {
        outline: 3px solid;
        outline-offset: 2px;
    }
}

/* Mobile-specific button improvements */
@media (max-width: 768px) {
    .btn, button {
        min-height: 48px; /* Larger touch targets for mobile */
        padding: 0.75rem 1.25rem;
        font-size: 1rem;
        border-radius: 12px;
        margin: 0.25rem;
        /* Enhanced touch feedback for mobile */
        -webkit-tap-highlight-color: rgba(139, 92, 246, 0.3);
        transform: translateZ(0); /* Enable hardware acceleration */
    }

    .btn:active, button:active {
        transform: scale(0.98) translateZ(0);
        transition: transform 0.1s ease;
    }

    .btn-sm {
        min-height: 44px; /* Increased from 40px for better touch */
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
        border-radius: 10px;
    }

    .btn-lg {
        min-height: 56px;
        padding: 1rem 2rem;
        font-size: 1.1rem;
        border-radius: 14px;
    }

    /* Improved spacing between interactive elements */
    .btn + .btn, button + button {
        margin-left: 0.5rem;
    }

    /* Better visual feedback for disabled state */
    .btn:disabled, button:disabled {
        opacity: 0.5;
        transform: none !important;
        cursor: not-allowed;
    }
}

/* Enhanced Touch-friendly form inputs */
@media (max-width: 768px) {
    input, select, textarea {
        min-height: 48px; /* Increased for better touch */
        padding: 0.875rem 1rem;
        font-size: max(1rem, 16px); /* Prevent zoom on iOS */
        border-radius: 12px;
        border: 2px solid #e5e7eb;
        transition: all 0.3s ease;
        -webkit-appearance: none;
        appearance: none;
    }

    input:focus, select:focus, textarea:focus {
        border-color: var(--primary-purple);
        box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.15);
        transform: scale(1.01);
        outline: none;
    }

    /* Enhanced select styling for mobile */
    select {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 0.75rem center;
        background-repeat: no-repeat;
        background-size: 1.5em 1.5em;
        padding-right: 3rem;
    }

    /* File input improvements */
    input[type="file"] {
        padding: 0.75rem;
        background: #f9fafb;
        border: 2px dashed #d1d5db;
        border-radius: 12px;
        cursor: pointer;
        min-height: 52px;
    }

    input[type="file"]:hover {
        border-color: var(--primary-purple);
        background: rgba(139, 92, 246, 0.05);
    }

    .form-control-sm {
        min-height: 44px; /* Increased from 40px */
        padding: 0.6rem 0.8rem;
        font-size: 0.95rem;
        border-radius: 10px;
    }

    /* Improved label styling */
    label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        display: block;
        color: #374151;
        font-size: 0.95rem;
    }

    /* Required field indicators */
    label.required::after {
        content: ' *';
        color: #ef4444;
        font-weight: bold;
    }
}

/* Enhanced Accessibility Features */

/* Screen reader only content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Skip to main content link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-purple);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 9999;
    transition: top 0.3s ease;
}

.skip-link:focus {
    top: 6px;
}

/* Enhanced focus indicators */
.btn:focus-visible, button:focus-visible,
input:focus-visible, select:focus-visible,
textarea:focus-visible, a:focus-visible {
    outline: 3px solid var(--primary-purple);
    outline-offset: 2px;
    box-shadow: 0 0 0 6px rgba(139, 92, 246, 0.2);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .btn::before, button::before {
        display: none;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1f2937;
        --text-color: #f9fafb;
        --border-color: #374151;
    }

    body {
        background-color: var(--bg-color);
        color: var(--text-color);
    }

    .modal-overlay > div {
        background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
        color: var(--text-color);
    }

    input, select, textarea {
        background-color: #374151;
        border-color: #4b5563;
        color: var(--text-color);
    }
}

@media (max-width: 576px) {
    #customSizeFields {
        flex-direction: column;
    }

    #customSizeFields input {
        width: 100%;
        margin-bottom: 0.75rem;
    }

    /* Enhanced mobile spacing */
    .btn + .btn {
        margin-top: 0.5rem;
        margin-left: 0;
    }

    /* Better mobile form layout */
    .form-group {
        margin-bottom: 1.5rem;
    }
}

#scaledPreviewWrapper {
    transform: scale(1);
    transform-origin: center center;
}

.content-area,
.row.g-4.align-items-stretch {
    display: flex;
    flex-wrap: nowrap;
    align-items: flex-start;
}

.col-lg-7,
.col-lg-5 {
    flex: 1;
    max-width: 50%;
}

.id-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 2px solid var(--border-color);
    transition: all 0.3s ease;
    min-height: 300px;
    box-sizing: border-box;
    margin: 0 auto;
    overflow: hidden;
}

.id-card.loaded {
    border-color: var(--success-color);
    box-shadow: 0 10px 30px rgba(5, 150, 105, 0.2);
}

.id-card-header {
    text-align: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.id-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
    margin: 0;
}

.employee-info {
    margin-top: 1rem;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.info-label {
    font-weight: 600;
    color: #64748b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.info-value {
    font-weight: 500;
    color: #1e293b;
    text-align: right;
}

/* Enhanced Scanner Controls */
.scanner-controls {
    text-align: center;
    margin-bottom: 2rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
    align-items: center;
}

/* Camera control buttons */
.scanner-controls .btn {
    min-height: 44px;
    min-width: 120px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

/* Responsive scanner controls */
@media (max-width: 480px) {
    .scanner-controls {
        flex-direction: column;
        gap: 0.75rem;
    }

    .scanner-controls .btn {
        width: 100%;
        max-width: 200px;
        min-height: 48px;
        font-size: 0.9rem;
    }
}

@media (max-width: 320px) {
    .scanner-controls .btn {
        min-height: 44px;
        font-size: 0.85rem;
        padding: 0.5rem 1rem;
    }
}

.scan-mode-toggle {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 2rem;
}

.mode-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid var(--border-color);
    background: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.mode-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.mode-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

#reader,
.scanner-section .border.bg-white.shadow-sm,
.border.rounded.p-3.bg-white.shadow-sm {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%) !important;
    border: 2px solid rgba(102, 126, 234, 0.3) !important;
    border-radius: 15px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

#reader:hover,
.scanner-section .border.bg-white.shadow-sm:hover,
.border.rounded.p-3.bg-white.shadow-sm:hover {
    border-color: rgba(102, 126, 234, 0.6) !important;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

#usb-input {
    width: 100%;
    padding: 1rem;
    font-size: 1.2rem;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    text-align: center;
    transition: all 0.3s ease;
    display: none;
}

#usb-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.btn-custom {
    padding: 0.5rem 1.2rem;
    border-radius: 8px;
    font-weight: 500;
    letter-spacing: 0.3px;
    transition: all 0.2s ease;
    border: none;
    display: flex;
    align-items: center;
    gap: 0.4rem;
    font-size: 0.9rem;
}

@media print {
    #paperBoundary {
        background: none !important;
        padding: 0 !important;
    }
    body {
        background: white !important;
    }
    .main-container,
    .preview-section,
    .id-card {
        box-shadow: none !important;
        border: none !important;
        margin: 0 !important;
        padding: 0 !important;
        width: 100%;
        height: 100%;
    }
    .id-card {
        page-break-before: always;
    }
    .scanner-section,
    .header,
    .action-buttons,
    .setting-btn,
    .status-indicator,
    .scanner-controls {
        display: none !important;
    }
    .preview-section {
        width: 100% !important;
        border: none !important;
        padding: 0 !important;
        margin: 0 auto !important;
    }
    #id-preview {
        width: 100%;
        height: 100%;
    }
}

.btn-print {
    background: var(--success-color);
    color: white;
}

.btn-print:hover {
    background: #047857;
    transform: translateY(-2px);
}

.btn-reset {
    background: var(--warning-color);
    color: white;
}

.btn-reset:hover {
    background: #b45309;
    transform: translateY(-2px);
}

.status-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--danger-color);
    animation: pulse 2s infinite;
}

.status-indicator.connected {
    background: var(--success-color);
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.loading-spinner {
    display: none;
    margin: 1rem auto;
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.alert-custom {
    border-radius: 10px;
    border: none;
    padding: 1rem;
    margin: 1rem 0;
    display: none;
}

/* Enhanced Tablet and iPad Optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
    /* iPad Portrait and Landscape */
    .container-fluid {
        padding: 1.5rem 2rem;
    }

    .card {
        padding: 2rem;
        margin-bottom: 1.5rem;
    }

    .preview-section,
    .scanner-section {
        padding: 2rem;
        min-height: 400px;
    }

    /* Better two-column layout for tablets */
    .d-flex.flex-column.flex-lg-row {
        flex-direction: row !important;
        gap: 2rem;
    }

    .preview-section {
        flex: 1 1 55%;
        max-width: 55%;
    }

    .scanner-section {
        flex: 1 1 45%;
        max-width: 45%;
    }

    /* Tablet-optimized buttons */
    .btn {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
        min-height: 48px;
    }

    /* Form improvements for tablets */
    .row.g-3 .col-md-4,
    .row.g-3 .col-md-5,
    .row.g-3 .col-md-3 {
        flex: 1;
        min-width: 200px;
    }
}

/* iPad Pro and larger tablets */
@media (min-width: 1024px) and (max-width: 1366px) {
    .container-fluid {
        padding: 2rem 3rem;
        max-width: 1200px;
        margin: 0 auto;
    }

    .preview-section {
        flex: 1 1 60%;
        max-width: 60%;
    }

    .scanner-section {
        flex: 1 1 40%;
        max-width: 40%;
    }
}

@media (max-width: 991px) {
    .content-area,
    .row.g-4.align-items-stretch {
        flex-direction: column;
        flex-wrap: wrap;
    }
    .col-lg-7,
    .col-lg-5 {
        max-width: 100%;
    }
    #reader {
        max-width: 100% !important;
    }
}

#scaledPreviewWrapper {
    transform: scale(1);
    transform-origin: center center;
}

#idPreviewContainer {
    background-color: white;
    position: relative;
    z-index: 1;
    box-sizing: border-box;
    overflow: hidden;
    border: 1px solid #ddd;
    border-radius: 5px;
    transform: scale(1);
    transition: transform 0.3s ease-in-out;
}

#paperBoundary {
    width: 500px;
    height: 500px;
    overflow: hidden;
    border-radius: 10px;
    margin: auto;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    /* Light border to contain the blur */
    border: 1px solid rgba(255, 255, 255, 0.2);
}

#paperBoundary::before {
    content: "";
    position: absolute;
    inset: -15px; /* Extend blur beyond container */
    background-image: inherit;
    background-size: cover;
    background-position: center;
    /* Softer blur with maintained brightness */
    filter: blur(12px) brightness(1);
    z-index: 0;
    /* Very light overlay for smoothness */
    background-color: rgba(255, 255, 255, 0.15);
    background-blend-mode: soft-light;
    /* Performance optimization */
    will-change: transform;
}

.id-overlay div {
    font-weight: 600;
    font-size: 1rem;
    color: #111827;
    /* Subtle white shadow for better contrast */
    text-shadow: 
        0 1px 1px rgba(255, 255, 255, 0.7),
        0 0 8px rgba(255, 255, 255, 0.4);
}

/* Specific styling for the header text */
.id-overlay div:first-child {
    font-size: 1.8rem;
    font-weight: 700;
    letter-spacing: 0.05em;
}

/* Styling for the ID number */
.id-overlay div:nth-child(2) {
    font-size: 1.5rem;
    margin: 0.5rem 0;
}

h5.mb-3 {
    background: #dfd4bd;
    color: #333;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    display: inline-block;
    font-weight: 600;
}

/* Section Header Styles */
.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    color: var(--primary-purple);
    font-weight: 600;
    font-size: 1.1rem;
}

.section-header i {
    color: var(--primary-purple);
    opacity: 0.8;
}

/* Email Configuration Styles */
.email-config-control {
    background: rgba(139, 92, 246, 0.03);
    border: 1px solid rgba(139, 92, 246, 0.15);
    border-radius: var(--border-radius-md);
    padding: 1.25rem;
    transition: var(--transition-medium);
    margin-top: 0.75rem;
}

.email-config-control:hover {
    background: rgba(139, 92, 246, 0.06);
    border-color: rgba(139, 92, 246, 0.25);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.1);
}

.email-config-note {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.5rem;
    font-style: italic;
}

#emailConfigStatus {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Password toggle button styling */
.input-group .btn-outline-secondary {
    border-color: #d1d5db;
    color: #6b7280;
    border-left: none;
    background: transparent;
    padding: 0.375rem 0.75rem;
}

.input-group .btn-outline-secondary:hover {
    background-color: rgba(139, 92, 246, 0.1);
    border-color: #d1d5db;
    color: var(--primary-purple);
}

.input-group .btn-outline-secondary:focus {
    box-shadow: none;
    border-color: #d1d5db;
    background-color: rgba(139, 92, 246, 0.1);
}

/* Make password input and button look seamless */
.input-group input.form-control {
    border-right: none;
}

.input-group input.form-control:focus {
    border-right: none;
    box-shadow: none;
}

.input-group input.form-control:focus + .btn-outline-secondary {
    border-color: #86b7fe;
}

/* Enhanced button styles for email config */
.email-config-control .btn {
    transition: all var(--transition-fast);
    font-weight: 500;
}

.email-config-control .btn-outline-success:hover {
    background: var(--success-color);
    border-color: var(--success-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(22, 163, 74, 0.3);
}

.email-config-control .btn-outline-primary:hover {
    background: var(--primary-purple);
    border-color: var(--primary-purple);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}
