#!/usr/bin/env python3
"""
Google OAuth2 Service for Gmail integration
Provides "Sign in with Google" functionality for email sending
"""

import os
import json
import base64
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.image import MIMEImage
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    from google.auth.transport.requests import Request
    from google.oauth2.credentials import Credentials
    from google_auth_oauthlib.flow import Flow
    from googleapiclient.discovery import build
    from googleapiclient.errors import HttpError
    GOOGLE_APIS_AVAILABLE = True
except ImportError:
    GOOGLE_APIS_AVAILABLE = False
    logger.warning("Google APIs not available. Install with: pip install google-auth google-auth-oauthlib google-api-python-client")

class GoogleOAuthService:
    """Google OAuth2 service for Gmail integration"""
    
    def __init__(self):
        self.credentials = None
        self.service = None
        self.user_email = None
        self.user_name = None
        
        # OAuth2 configuration
        self.client_config = {
            "web": {
                "client_id": os.getenv('GOOGLE_CLIENT_ID', ''),
                "client_secret": os.getenv('GOOGLE_CLIENT_SECRET', ''),
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token",
                "redirect_uris": ["http://localhost:5000/oauth2callback"]
            }
        }
        
        self.scopes = [
            'https://www.googleapis.com/auth/gmail.send',
            'https://www.googleapis.com/auth/userinfo.email',
            'https://www.googleapis.com/auth/userinfo.profile'
        ]
        
        # Load existing credentials if available
        self.load_credentials()
    
    def is_available(self):
        """Check if Google APIs are available"""
        return GOOGLE_APIS_AVAILABLE
    
    def is_configured(self):
        """Check if OAuth2 is properly configured"""
        return (
            self.client_config["web"]["client_id"] and 
            self.client_config["web"]["client_secret"]
        )
    
    def is_authenticated(self):
        """Check if user is authenticated"""
        return self.credentials and self.credentials.valid
    
    def get_auth_url(self):
        """Get the authorization URL for OAuth2 flow"""
        if not self.is_available():
            raise Exception("Google APIs not available")
        
        if not self.is_configured():
            raise Exception("OAuth2 not configured. Please set GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET")
        
        flow = Flow.from_client_config(
            self.client_config,
            scopes=self.scopes
        )
        flow.redirect_uri = "http://localhost:5000/oauth2callback"
        
        auth_url, _ = flow.authorization_url(
            access_type='offline',
            include_granted_scopes='true',
            prompt='consent'
        )
        
        return auth_url
    
    def handle_oauth_callback(self, authorization_code):
        """Handle OAuth2 callback and exchange code for tokens"""
        if not self.is_available():
            raise Exception("Google APIs not available")
        
        flow = Flow.from_client_config(
            self.client_config,
            scopes=self.scopes
        )
        flow.redirect_uri = "http://localhost:5000/oauth2callback"
        
        # Exchange authorization code for tokens
        flow.fetch_token(code=authorization_code)
        
        self.credentials = flow.credentials
        self.save_credentials()
        
        # Initialize Gmail service and get user info
        self.initialize_service()
        self.get_user_info()
        
        return True
    
    def initialize_service(self):
        """Initialize Gmail service with credentials"""
        if not self.credentials:
            raise Exception("No credentials available")
        
        self.service = build('gmail', 'v1', credentials=self.credentials)
    
    def get_user_info(self):
        """Get user information from Google"""
        if not self.credentials:
            return
        
        try:
            # Get user info from Google
            user_service = build('oauth2', 'v2', credentials=self.credentials)
            user_info = user_service.userinfo().get().execute()
            
            self.user_email = user_info.get('email', '')
            self.user_name = user_info.get('name', 'User')
            
            logger.info(f"Authenticated as: {self.user_name} ({self.user_email})")
            
        except Exception as e:
            logger.error(f"Failed to get user info: {e}")
    
    def save_credentials(self):
        """Save credentials to file"""
        if not self.credentials:
            return
        
        creds_data = {
            'token': self.credentials.token,
            'refresh_token': self.credentials.refresh_token,
            'token_uri': self.credentials.token_uri,
            'client_id': self.credentials.client_id,
            'client_secret': self.credentials.client_secret,
            'scopes': self.credentials.scopes
        }
        
        with open('google_credentials.json', 'w') as f:
            json.dump(creds_data, f)
    
    def load_credentials(self):
        """Load credentials from file"""
        if not os.path.exists('google_credentials.json'):
            return
        
        try:
            with open('google_credentials.json', 'r') as f:
                creds_data = json.load(f)
            
            self.credentials = Credentials(
                token=creds_data['token'],
                refresh_token=creds_data.get('refresh_token'),
                token_uri=creds_data['token_uri'],
                client_id=creds_data['client_id'],
                client_secret=creds_data['client_secret'],
                scopes=creds_data['scopes']
            )
            
            # Refresh if needed
            if self.credentials.expired and self.credentials.refresh_token:
                self.credentials.refresh(Request())
                self.save_credentials()
            
            if self.credentials.valid:
                self.initialize_service()
                self.get_user_info()
                
        except Exception as e:
            logger.error(f"Failed to load credentials: {e}")
            self.credentials = None
    
    def send_email(self, recipient_email, recipient_name, qr_image_path, employee_data):
        """Send email using Gmail API"""
        if not self.is_authenticated():
            return False, "Not authenticated with Google"
        
        try:
            # Create message
            msg = MIMEMultipart('related')
            msg['Subject'] = f"Your Employee QR Code - {employee_data.get('Name', 'Employee')}"
            msg['From'] = f"{self.user_name} <{self.user_email}>"
            msg['To'] = recipient_email
            
            # Create HTML content (reuse from email_service.py)
            html_content = self._create_html_template(recipient_name, employee_data)
            
            # Attach HTML content
            html_part = MIMEText(html_content, 'html')
            msg.attach(html_part)
            
            # Attach QR code image
            if os.path.exists(qr_image_path):
                with open(qr_image_path, 'rb') as f:
                    img_data = f.read()
                    img = MIMEImage(img_data, _subtype='png')
                    img.add_header('Content-ID', '<qr_code>')
                    img.add_header('Content-Disposition', 'attachment', 
                                 filename=f"qr_code_{employee_data.get('ID', 'unknown')}.png")
                    msg.attach(img)
            
            # Encode message
            raw_message = base64.urlsafe_b64encode(msg.as_bytes()).decode('utf-8')
            
            # Send via Gmail API
            message = {'raw': raw_message}
            result = self.service.users().messages().send(userId='me', body=message).execute()
            
            logger.info(f"Email sent successfully to {recipient_email} via Gmail API")
            return True, "Email sent successfully"
            
        except HttpError as e:
            error_msg = f"Gmail API error: {e}"
            logger.error(error_msg)
            return False, error_msg
        except Exception as e:
            error_msg = f"Failed to send email: {e}"
            logger.error(error_msg)
            return False, error_msg
    
    def _create_html_template(self, recipient_name, employee_data):
        """Create HTML email template (same as email_service.py)"""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Your Employee QR Code</title>
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #f9f9f9;
                }}
                .container {{
                    background: white;
                    border-radius: 10px;
                    padding: 30px;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                }}
                .header {{
                    text-align: center;
                    border-bottom: 3px solid #007bff;
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                }}
                .header h1 {{
                    color: #007bff;
                    margin: 0;
                    font-size: 28px;
                }}
                .qr-section {{
                    text-align: center;
                    margin: 30px 0;
                    padding: 20px;
                    background: #f8f9fa;
                    border-radius: 8px;
                }}
                .employee-info {{
                    background: #e9ecef;
                    padding: 20px;
                    border-radius: 8px;
                    margin: 20px 0;
                }}
                .info-row {{
                    display: flex;
                    justify-content: space-between;
                    margin: 10px 0;
                    padding: 8px 0;
                    border-bottom: 1px solid #dee2e6;
                }}
                .info-label {{
                    font-weight: bold;
                    color: #495057;
                }}
                .info-value {{
                    color: #212529;
                }}
                .footer {{
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #dee2e6;
                    color: #6c757d;
                    font-size: 14px;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🆔 Employee QR Code</h1>
                    <p>Your personal identification QR code</p>
                </div>
                
                <p>Hello <strong>{recipient_name}</strong>,</p>
                
                <p>Your employee QR code has been generated and is attached to this email. This QR code contains your identification information and can be used for secure access and verification.</p>
                
                <div class="qr-section">
                    <h3>📱 QR Code Attached</h3>
                    <p>Please find your QR code image attached to this email.</p>
                </div>
                
                <div class="employee-info">
                    <h3>👤 Employee Information</h3>
                    <div class="info-row">
                        <span class="info-label">ID:</span>
                        <span class="info-value">{employee_data.get('ID', 'N/A')}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Name:</span>
                        <span class="info-value">{employee_data.get('Name', 'N/A')}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Position:</span>
                        <span class="info-value">{employee_data.get('Position', 'N/A')}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Company:</span>
                        <span class="info-value">{employee_data.get('Company', 'N/A')}</span>
                    </div>
                </div>
                
                <p><strong>Important:</strong> Please keep this QR code secure and do not share it with unauthorized individuals.</p>
                
                <div class="footer">
                    <p>This email was sent automatically by the ID QR System.<br>
                    If you have any questions, please contact your system administrator.</p>
                </div>
            </div>
        </body>
        </html>
        """
    
    def logout(self):
        """Logout and clear credentials"""
        self.credentials = None
        self.service = None
        self.user_email = None
        self.user_name = None
        
        # Remove credentials file
        if os.path.exists('google_credentials.json'):
            os.remove('google_credentials.json')
        
        logger.info("Logged out successfully")

# Global OAuth service instance
google_oauth_service = GoogleOAuthService()
