
#!/usr/bin/env python3
"""
Simple startup script for the Smart QR ID Scanner application
This ensures we're running the main application, not test files
"""

import os
import sys

def main():
    """Start the main application"""
    print("🚀 Starting Smart QR ID Scanner Application...")
    print("=" * 50)
    
    # Ensure we're in the right directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # Import and run the main app
    try:
        from app import app
        import threading
        import webview
        import time
        import socket

        # Get configuration from environment variables
        host = os.getenv('FLASK_HOST', '0.0.0.0')  # Allow external connections
        port = int(os.getenv('FLASK_PORT', '5000'))
        debug_mode = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
        desktop_mode = os.getenv('DESKTOP_MODE', 'true').lower() == 'true'

        print("✅ Starting Flask server...")
        print(f"   Host: {host}")
        print(f"   Port: {port}")
        print(f"   Desktop Mode: {desktop_mode}")

        def run_flask():
            app.run(debug=debug_mode, host=host, port=port, use_reloader=False)

        # Start Flask in background thread
        flask_thread = threading.Thread(target=run_flask)
        flask_thread.daemon = True
        flask_thread.start()

        # Wait for Flask to start
        time.sleep(3)

        # Get network information for mobile access
        try:
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
        except:
            local_ip = "localhost"

        print("✅ Server started successfully!")
        print("🌐 Access URLs:")
        print(f"   Desktop/Local: http://localhost:{port}")
        print(f"   Mobile/Network: http://{local_ip}:{port}")
        print("📱 For mobile access, connect to the same WiFi network")

        if desktop_mode:
            print("✅ Opening desktop application window...")

            # Create and start webview window with mobile-friendly options
            webview.create_window(
                "Smart QR ID Scanner",
                f"http://localhost:{port}",
                width=1280,
                height=720,
                resizable=True,
                fullscreen=False,
                frameless=False,
                # Enhanced mobile and responsive support
                min_size=(320, 568),  # iPhone SE minimum size
                on_top=False,
                shadow=True
            )

            print("🎯 Application is starting...")
            print("📧 Email functionality has been fixed!")
            print("   - Better error messages")
            print("   - Gmail App Password instructions")
            print("   - Clear guidance for users")
            print("📱 Mobile responsive design enabled!")
            print("   - Touch-friendly interface")
            print("   - Adaptive layouts")
            print("   - Cross-device compatibility")

            webview.start(gui='edgechromium')
        else:
            print("🌐 Running in web server mode only")
            print("   Access the application using the URLs above")
            # Keep Flask running
            flask_thread.join()
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please ensure all dependencies are installed:")
        print("pip install flask pandas qrcode pillow cryptography reportlab python-dotenv pywebview")
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        print("Please check that all files are present and dependencies are installed.")

if __name__ == "__main__":
    main()