
#!/usr/bin/env python3
"""
Simple startup script for the Smart QR ID Scanner application
This ensures we're running the main application, not test files
"""

import os
import sys

def main():
    """Start the main application"""
    print("🚀 Starting Smart QR ID Scanner Application...")
    print("=" * 50)
    
    # Ensure we're in the right directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # Import and run the main app
    try:
        from app import app
        import threading
        import webview
        import time
        
        print("✅ Starting Flask server...")
        
        def run_flask():
            app.run(debug=False, port=5000, use_reloader=False)
        
        # Start Flask in background thread
        flask_thread = threading.Thread(target=run_flask)
        flask_thread.daemon = True
        flask_thread.start()
        
        # Wait for Flask to start
        time.sleep(2)
        
        print("✅ Opening application window...")
        
        # Create and start webview window
        webview.create_window(
            "Smart QR ID Scanner",
            "http://localhost:5000",
            width=1280,
            height=720,
            resizable=True,
            fullscreen=False,
            frameless=False
        )
        
        print("🎯 Application is starting...")
        print("📧 Email functionality has been fixed!")
        print("   - Better error messages")
        print("   - Gmail App Password instructions")
        print("   - Clear guidance for users")
        
        webview.start(gui='edgechromium')
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please ensure all dependencies are installed:")
        print("pip install flask pandas qrcode pillow cryptography reportlab python-dotenv pywebview")
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        print("Please check that all files are present and dependencies are installed.")

if __name__ == "__main__":
    main()